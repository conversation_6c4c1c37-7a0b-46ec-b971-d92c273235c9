mod db;
mod models;
mod xml_processor;
mod external_api;
mod task_runner;
mod scheduler;
mod http_server;

use std::sync::Arc;
use tokio::signal;
use tracing_subscriber::{
    fmt::{self, time::OffsetTime},
    layer::SubscriberExt, // 扩展方法，如 .with
    util::SubscriberInitExt, // 用于 .init()
    EnvFilter, // 用于从环境变量配置日志级别
};
use tracing_appender::rolling; // 用于滚动日志文件
use tracing_appender::non_blocking::WorkerGuard; // Import WorkerGuard
use mimalloc::MiMalloc;

#[global_allocator]
static GLOBAL: MiMalloc = MiMalloc;

fn setup_logging() -> Result<WorkerGuard, anyhow::Error> {
    // 配置日志文件：每天轮换一次，存储在当前目录下的 logs 文件夹中，文件名为 workov.yyyy-MM-dd.log
    let file_appender = rolling::daily("logs", "workov.log");
    let (non_blocking_file_writer, guard) = tracing_appender::non_blocking(file_appender);

    // 从 RUST_LOG 环境变量（如果设置了）或默认级别 "info" 配置日志过滤
    // 例如 RUST_LOG=workov=debug,tokio=info
    let env_filter = EnvFilter::try_from_default_env().unwrap_or_else(|_| EnvFilter::new("info"));

    // 创建一个 Timer 来格式化本地时间
    let local_timer = OffsetTime::local_rfc_3339().expect("could not get local offset!");

    tracing_subscriber::registry()
        .with(env_filter)
        .with(fmt::layer().with_writer(std::io::stdout).with_timer(local_timer.clone())) // 输出到控制台，使用本地时间
        .with(fmt::layer().json().with_writer(non_blocking_file_writer).with_timer(local_timer)) // 输出 JSON 格式到文件，使用本地时间
        .init();
    
    Ok(guard) // Return the guard
}

#[tokio::main]
async fn main() {
    // Hold the guard for the duration of the main function
    let _logger_guard = match setup_logging() { 
        Ok(guard) => Some(guard),
        Err(e) => {
            eprintln!("Failed to set up logging: {:?}", e);
            None
        }
    };

    tracing::info!("Application starting up...");

    //dotenvy::dotenv().ok(); // Keep general .env loading for other potential runtime vars, if any.

    let company_ids = Arc::new(vec!["24".to_string(), "1".to_string()]);
    let http_server_port = 3000;

    let company_ids_for_http = Arc::clone(&company_ids);
    let http_server_handle = tokio::spawn(async move {
        if let Err(e) = http_server::start_http_server(http_server_port, company_ids_for_http).await {
            tracing::error!(error = ?e, "HTTP server failed");
        }
    });
    tracing::info!("HTTP server task spawned.");

    let company_ids_for_scheduler = Arc::clone(&company_ids);
    let mut scheduler_instance = match scheduler::start_scheduler(company_ids_for_scheduler).await {
        Ok(sched) => {
            tracing::info!("Scheduler initialized and started successfully.");
            Some(sched)
        }
        Err(e) => {
            tracing::error!(error = ?e, "Failed to start scheduler");
            None
        }
    };

    if scheduler_instance.is_some() || !http_server_handle.is_finished() {
        tracing::info!("Scheduler and/or HTTP server are running. Press Ctrl+C to exit.");
        match signal::ctrl_c().await {
            Ok(()) => {
                tracing::info!("Ctrl+C received, shutting down services...");
            }
            Err(err) => {
                tracing::error!(error = ?err, "Failed to listen for Ctrl+C. Forcing shutdown attempt.");
            }
        }
    } else {
        tracing::warn!("Neither scheduler nor HTTP server started correctly. Application will exit.");
    }

    tracing::info!("Shutting down HTTP server...");
    http_server_handle.abort();
    if let Err(e) = http_server_handle.await {
        if !e.is_cancelled() {
             tracing::error!(error = ?e, "Error during HTTP server shutdown");
        }
    }
    tracing::info!("HTTP server shutdown signal sent.");

    if let Some(mut sched) = scheduler_instance.take() {
        tracing::info!("Shutting down scheduler...");
        if let Err(e) = sched.shutdown().await {
            tracing::error!(error = ?e, "Error shutting down scheduler");
        } else {
            tracing::info!("Scheduler shut down gracefully.");
        }
    }
    
    tracing::info!("Application has shut down.");
    // _logger_guard is dropped here, ensuring logs are flushed.
}
