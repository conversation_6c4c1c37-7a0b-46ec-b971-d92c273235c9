use crate::models::WorkInfo;
use anyhow::{Result, anyhow};
use std::env;
use tokio_postgres::{connect, NoTls, Row, Client};
use uuid::Uuid;
use tracing;

// Maps a database row to the WorkInfo struct
fn row_to_workinfo(row: &Row) -> Result<WorkInfo, tokio_postgres::Error> {
    Ok(WorkInfo {
        workcode: row.try_get("workcode")?,
        full_name: row.try_get("full_name")?,
        oa_uid: row.try_get("oa_uid")?,
        company_id: row.try_get("company_id")?,
        ov_date: row.try_get("ov_date")?,
        start_time: row.try_get("start_time")?,
        end_time: row.try_get("end_time")?,
        ov_hours: row.try_get("ov_hours")?,
        ov_reason: row.try_get("ov_reason")?,
        workflag: row.try_get("workflag")?,
        submit_status: row.try_get("submit_status")?,
        requestid: row.try_get("requestid")?,
        submit_id: row.try_get("submit_id")?,
    })
}

// Helper to get a DB client
async fn get_db_client() -> Result<Client> {
    let db_url = env!("DATABASE_URL");
    let (client, connection) = connect(db_url, NoTls).await?;
    tokio::spawn(async move {
        if let Err(e) = connection.await {
            tracing::error!(error = ?e, "DB connection error in spawned task");
        }
    });
    Ok(client)
}

// Generates a unique ID for batch processing
fn generate_unique_submit_id() -> String {
    Uuid::new_v4().to_string()
}

// Step 1: Set a unique submit_id for pending items and return the ID and affected count
pub async fn set_submit_id_for_pending(company_id: &str) -> Result<(Option<String>, u64)> {
    let client = get_db_client().await?;
    let submit_id = generate_unique_submit_id();

    let update_query = 
    "UPDATE workinfo wi  
SET submit_id = $1 
FROM employee ep  
WHERE wi.workcode =ep.workcode 
AND wi.ov_date >= (current_date - INTERVAL '3 days')
AND ep.company_id = $2 
AND (submit_status = 0 OR submit_status = 2) 
AND submit_id IS NULL";
    
    tracing::info!(company_id = %company_id, submit_id = %submit_id, "Attempting to tag pending items");
    match client.execute(update_query, &[&submit_id, &company_id]).await {
        Ok(affected_rows) => {
            if affected_rows > 0 {
                tracing::info!(company_id = %company_id, submit_id = %submit_id, count = affected_rows, "Tagged items successfully");
                Ok((Some(submit_id), affected_rows))
            } else {
                tracing::info!(company_id = %company_id, "No items to tag with current criteria.");
                Ok((None, 0))
            }
        }
        Err(e) => {
            tracing::error!(company_id = %company_id, submit_id = %submit_id, error = ?e, "Error setting submit_id");
            Err(anyhow!("Failed to set submit_id: {}", e))
        }
    }
}

// Step 2: Get work items based on the generated submit_id
pub async fn get_workitems_by_submit_id(submit_id: &str) -> Result<Vec<WorkInfo>> {
    let client = get_db_client().await?;
    let query_string = 
    "SELECT wi.workcode, ep.full_name, ep.oa_uid, ep.company_id, 
    wi.ov_date, wi.start_time, wi.end_time, wi.ov_hours, wi.ov_reason, wi.workflag, wi.submit_status, wi.requestid, wi.submit_id 
    FROM workinfo wi JOIN employee ep ON wi.workcode = ep.workcode 
    WHERE wi.submit_id = $1
    ORDER BY wi.ov_date,wi.workcode, wi.start_time";
    
    tracing::info!(submit_id = %submit_id, "Fetching items by submit_id");
    let rows = client.query(query_string, &[&submit_id]).await?;

    let mut work_items = Vec::new();
    if rows.is_empty() {
        tracing::warn!(submit_id = %submit_id, "No items found for submit_id, although they were expected.");
    }
    for row in rows {
        work_items.push(row_to_workinfo(&row).map_err(|e| anyhow!("Row to WorkInfo conversion failed: {}",e))?);
    }
    Ok(work_items)
}

// Step 3: Update final status based on submit_id
pub async fn update_workitems_status_by_submit_id(
    submit_id_to_update: &str,
    request_id_on_success: Option<&str>,
    new_status: i32, // 1 for success, 2 for failure
) -> Result<u64> { // Return affected rows count
    let client = get_db_client().await?;
    // No need for explicit transaction for a single batch update, 
    // but can be kept if preferred for atomicity with other potential steps in the future.
    // For simplicity, let's do a direct execution for now.

    tracing::info!(
        submit_id = %submit_id_to_update, 
        target_status = new_status, 
        api_request_id = ?request_id_on_success, 
        "Updating items to final status"
    );

    let affected_rows = if new_status == 1 {
        if let Some(r_id) = request_id_on_success {
            // Success: set status, requestid, and clear submit_id
            let query = "UPDATE workinfo SET submit_status = $1, requestid = $2 WHERE submit_id = $3";
            client.execute(query, &[&new_status, &r_id, &submit_id_to_update]).await?
        } else {
            tracing::warn!(
                submit_id = %submit_id_to_update,
                "Attempting to update to success status 1 but no request_id was provided. Setting status to 1 and clearing submit_id only."
            );
            let query = "UPDATE workinfo SET submit_status = $1 WHERE submit_id = $2";
            client.execute(query, &[&new_status, &submit_id_to_update]).await?
        }
    } else { // For failure (status 2 or other non-1 statuses)
        // Failure: set status, and clear submit_id
        let query = "UPDATE workinfo SET submit_status = $1 WHERE submit_id = $2";
        client.execute(query, &[&new_status, &submit_id_to_update]).await?
    };

    if affected_rows == 0 {
        tracing::warn!(
            submit_id = %submit_id_to_update,
            "No rows updated for final status. Items might have been manually changed or an issue occurred."
        );
    } else {
        tracing::info!(
            submit_id = %submit_id_to_update, 
            count = affected_rows, 
            final_status = new_status,
            "Successfully updated items."
        );
    }
    Ok(affected_rows)
}